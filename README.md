# TXT to Excel Processor for Windows Task Scheduler

This Python script processes .txt files in a target folder and creates an Excel file with the contents organized in columns.

## Features

- Scans a target folder for all .txt files
- Creates an Excel file with timestamp in the filename
- Organizes data with line numbers in Column A and file contents in subsequent columns
- Includes a summary row with all processed filenames
- Comprehensive logging to both file and console
- Error handling for missing folders, unreadable files, etc.

## Setup

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Edit the script `txt_to_excel_processor.py`:
   - Replace `PLACEHOLDER_FOLDER_PATH` with your actual target folder path
   - Example: `TARGET_FOLDER = r"C:\Users\<USER>\Documents\TextFiles"`

## Usage

### Manual Execution
```bash
python txt_to_excel_processor.py
```

### Windows Task Scheduler Setup

1. Open Task Scheduler (taskschd.msc)
2. Create Basic Task or Create Task
3. Set your desired schedule (daily, weekly, etc.)
4. For the Action:
   - Program/script: `python` (or full path to python.exe)
   - Add arguments: `txt_to_excel_processor.py`
   - Start in: `C:\path\to\your\script\directory`

## Output

- Excel file named: `txt_files_processed_YYYYMMDD_HHMMSS.xlsx`
- Log file: `txt_processor.log`
- Both files are created in the same directory as the script

## Excel File Structure

- **Column A**: Line numbers (Line_1, Line_2, etc.)
- **Columns B+**: Content from each .txt file
- **Summary row**: Lists all processed filenames

## OneDrive/SharePoint Considerations

- Ensure the target folder is synced locally
- Consider using local paths rather than cloud URLs
- Test the script manually before scheduling
- Monitor the log file for sync-related issues

## Troubleshooting

- Check `txt_processor.log` for detailed error messages
- Ensure Python is in your system PATH
- Verify the target folder path is accessible
- Test with a small number of .txt files first
