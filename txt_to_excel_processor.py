#!/usr/bin/env python3
"""
Task Scheduler Script: Process .txt files and create Excel output
This script scans a target folder for .txt files and creates an Excel file
with filenames in Column A and file contents in subsequent columns.
"""

import os
import sys
from datetime import datetime
from pathlib import Path
import openpyxl
from openpyxl import Workbook
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('txt_processor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def process_txt_files_to_excel(target_folder_path):
    """
    Process all .txt files in the target folder and create an Excel file.
    
    Args:
        target_folder_path (str): Path to the folder containing .txt files
    
    Returns:
        str: Path to the created Excel file, or None if failed
    """
    try:
        # Validate target folder
        target_folder = Path(target_folder_path)
        if not target_folder.exists():
            logging.error(f"Target folder does not exist: {target_folder_path}")
            return None
        
        if not target_folder.is_dir():
            logging.error(f"Target path is not a directory: {target_folder_path}")
            return None
        
        # Find all .txt files
        txt_files = list(target_folder.glob("*.txt"))
        if not txt_files:
            logging.warning(f"No .txt files found in: {target_folder_path}")
            return None
        
        logging.info(f"Found {len(txt_files)} .txt files to process")
        
        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "TXT Files Data"
        
        # Read all txt files and their contents
        all_files_data = []  # List of tuples: (filename, lines)
        all_lines = []  # All lines from all files sequentially

        for txt_file in sorted(txt_files):  # Sort for consistent ordering
            try:
                with open(txt_file, 'r', encoding='utf-8', errors='replace') as f:
                    lines = [line.rstrip('\n\r') for line in f.readlines()]
                    all_files_data.append((txt_file.name, lines))
                    all_lines.extend(lines)
                    logging.info(f"Read {len(lines)} lines from {txt_file.name}")
            except UnicodeDecodeError as e:
                logging.warning(f"Unicode decode error in {txt_file.name}, trying with different encoding: {e}")
                try:
                    with open(txt_file, 'r', encoding='latin-1') as f:
                        lines = [line.rstrip('\n\r') for line in f.readlines()]
                        all_files_data.append((txt_file.name, lines))
                        all_lines.extend(lines)
                        logging.info(f"Read {len(lines)} lines from {txt_file.name} with latin-1 encoding")
                except Exception as e2:
                    logging.error(f"Failed to read {txt_file.name} with any encoding: {e2}")
                    continue
            except PermissionError as e:
                logging.error(f"Permission denied reading {txt_file.name}: {e}")
                continue
            except Exception as e:
                logging.error(f"Unexpected error reading {txt_file.name}: {e}")
                continue

        if not all_files_data:
            logging.error("No files could be read successfully")
            return None

        if not all_lines:
            logging.warning("All files were empty")
            return None

        logging.info(f"Total lines collected from all files: {len(all_lines)}")

        # Create headers: Column A for filenames, Columns B+ for all lines sequentially
        ws.cell(row=1, column=1, value="Filename")
        for i, line in enumerate(all_lines):
            ws.cell(row=1, column=i + 2, value=f"Line_{i + 1}")

        # Write filenames in Column A and all lines sequentially in Columns B+
        row_num = 2
        for filename, lines in all_files_data:
            ws.cell(row=row_num, column=1, value=filename)
            row_num += 1

        # Write all lines sequentially starting from Column B, Row 2
        col_num = 2
        for line in all_lines:
            ws.cell(row=2, column=col_num, value=line)
            col_num += 1
        
        # Generate output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"txt_files_processed_{timestamp}.xlsx"
        output_path = target_folder / output_filename
        
        # Save the Excel file
        wb.save(output_path)
        logging.info(f"Excel file created successfully: {output_path}")
        
        return str(output_path)
        
    except Exception as e:
        logging.error(f"Unexpected error in process_txt_files_to_excel: {e}")
        return None

def main():
    """
    Main function to run the script.
    Replace PLACEHOLDER_FOLDER_PATH with your actual target folder path.
    """
    # PLACEHOLDER - Replace this with your actual folder path
    TARGET_FOLDER = r"PLACEHOLDER_FOLDER_PATH"

    # Basic validation of placeholder
    if TARGET_FOLDER == "PLACEHOLDER_FOLDER_PATH":
        error_msg = "ERROR: Please replace PLACEHOLDER_FOLDER_PATH with your actual folder path in the script"
        logging.error(error_msg)
        print(error_msg)
        sys.exit(1)

    logging.info("Starting TXT to Excel processing script")
    logging.info(f"Target folder: {TARGET_FOLDER}")

    try:
        # Process the files
        result_path = process_txt_files_to_excel(TARGET_FOLDER)

        if result_path:
            logging.info(f"Processing completed successfully. Output file: {result_path}")
            print(f"SUCCESS: Excel file created at {result_path}")
        else:
            logging.error("Processing failed")
            print("ERROR: Processing failed. Check the log file for details.")
            sys.exit(1)

    except KeyboardInterrupt:
        logging.info("Script interrupted by user")
        print("Script interrupted by user")
        sys.exit(0)
    except Exception as e:
        logging.error(f"Unexpected error in main: {e}")
        print(f"FATAL ERROR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
