#!/usr/bin/env python3
"""
Task Scheduler Script: Process .txt files and create Excel output
This script scans a target folder for .txt files and creates an Excel file
with filenames in Column A and file contents in subsequent columns.
"""

import os
import sys
from datetime import datetime
from pathlib import Path
import openpyxl
from openpyxl import Workbook
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('txt_processor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def process_txt_files_to_excel(target_folder_path):
    """
    Process all .txt files in the target folder and create an Excel file.
    
    Args:
        target_folder_path (str): Path to the folder containing .txt files
    
    Returns:
        str: Path to the created Excel file, or None if failed
    """
    try:
        # Validate target folder
        target_folder = Path(target_folder_path)
        if not target_folder.exists():
            logging.error(f"Target folder does not exist: {target_folder_path}")
            return None
        
        if not target_folder.is_dir():
            logging.error(f"Target path is not a directory: {target_folder_path}")
            return None
        
        # Find all .txt files
        txt_files = list(target_folder.glob("*.txt"))
        if not txt_files:
            logging.warning(f"No .txt files found in: {target_folder_path}")
            return None
        
        logging.info(f"Found {len(txt_files)} .txt files to process")
        
        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "TXT Files Data"
        
        # Read all txt files and their contents
        file_data = {}
        max_lines = 0
        
        for txt_file in txt_files:
            try:
                with open(txt_file, 'r', encoding='utf-8') as f:
                    lines = [line.rstrip('\n\r') for line in f.readlines()]
                    file_data[txt_file.name] = lines
                    max_lines = max(max_lines, len(lines))
                    logging.info(f"Read {len(lines)} lines from {txt_file.name}")
            except Exception as e:
                logging.error(f"Error reading {txt_file.name}: {e}")
                continue
        
        if not file_data:
            logging.error("No files could be read successfully")
            return None
        
        # Set up headers
        headers = ["Filename"] + [f"File_{i+1}_Content" for i in range(len(file_data))]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Write data to Excel
        # Each row represents a line number across all files
        for line_num in range(max_lines):
            row_num = line_num + 2  # Start from row 2 (after headers)
            
            # Column A: Show line number for reference
            ws.cell(row=row_num, column=1, value=f"Line_{line_num + 1}")
            
            # Subsequent columns: Content from each file
            col_num = 2
            for filename, lines in file_data.items():
                if line_num < len(lines):
                    ws.cell(row=row_num, column=col_num, value=lines[line_num])
                else:
                    ws.cell(row=row_num, column=col_num, value="")  # Empty if file has fewer lines
                col_num += 1
        
        # Add a summary row with filenames
        summary_row = max_lines + 3
        ws.cell(row=summary_row, column=1, value="Filenames:")
        col_num = 2
        for filename in file_data.keys():
            ws.cell(row=summary_row, column=col_num, value=filename)
            col_num += 1
        
        # Generate output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"txt_files_processed_{timestamp}.xlsx"
        output_path = target_folder / output_filename
        
        # Save the Excel file
        wb.save(output_path)
        logging.info(f"Excel file created successfully: {output_path}")
        
        return str(output_path)
        
    except Exception as e:
        logging.error(f"Unexpected error in process_txt_files_to_excel: {e}")
        return None

def main():
    """
    Main function to run the script.
    Replace PLACEHOLDER_FOLDER_PATH with your actual target folder path.
    """
    # PLACEHOLDER - Replace this with your actual folder path
    TARGET_FOLDER = r"PLACEHOLDER_FOLDER_PATH"
    
    logging.info("Starting TXT to Excel processing script")
    logging.info(f"Target folder: {TARGET_FOLDER}")
    
    # Process the files
    result_path = process_txt_files_to_excel(TARGET_FOLDER)
    
    if result_path:
        logging.info(f"Processing completed successfully. Output file: {result_path}")
        print(f"SUCCESS: Excel file created at {result_path}")
    else:
        logging.error("Processing failed")
        print("ERROR: Processing failed. Check the log file for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
